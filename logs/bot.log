2025-07-13 16:19:51,708 - __main__ - ERROR - Bot error in event on_ready
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 441, in _run_event
    await coro(*args, **kwargs)
  File "/root/chartfix/bot.py", line 210, in on_ready
    if not self.daily_market_report.is_running():
AttributeError: 'CryptoBot' object has no attribute 'daily_market_report'
2025-07-13 16:26:18,021 - __main__ - WARNING - Slow command execution: watchlist took 8.17s
2025-07-13 17:03:19,203 - __main__ - WARNING - Slow command execution: watchlist took 10.58s
2025-07-13 17:04:32,598 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 8 messages from #trade-h (keep_pinned: False)
2025-07-13 17:04:47,451 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 8 messages from #trade (keep_pinned: False)
2025-07-13 17:04:59,733 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x7f1ab5d8b520>, 13457.035844839)]']
connector: <aiohttp.connector.TCPConnector object at 0x7f1ad6a86d70>
2025-07-13 17:07:06,385 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #📈-market-data (keep_pinned: False)
2025-07-13 17:07:20,836 - __main__ - WARNING - Slow command execution: watchlist took 8.20s
2025-07-13 17:08:00,639 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 19 messages from #📝-bot-logs (keep_pinned: True)
2025-07-13 19:01:38,121 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-07-13 19:50:45,111 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 42.4s behind.
2025-07-13 20:01:07,187 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 42.4s behind.
2025-07-13 20:38:35,403 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0b77a458f506cc756e693b0122a226d3482231139008d2d681f6c402e0837c01
2025-07-13 20:50:02,548 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a906299d5f5adc8c96f6794123e927d428c2a9d406b5d444fe6336ca2b597fed
2025-07-14 00:19:39,301 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bf8f96d9ccb055926a8ac429854c566248c0c07571f8863c243e9fedd01cbaa7
2025-07-14 00:26:52,198 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=50709b5487dcb1b850034145f1f3fff74e4a5477b941e9665ddb850f711abab0
2025-07-14 00:26:52,198 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=50709b5487dcb1b850034145f1f3fff74e4a5477b941e9665ddb850f711abab0 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=50709b5487dcb1b850034145f1f3fff74e4a5477b941e9665ddb850f711abab0)
2025-07-14 03:05:58,794 - discord.client - ERROR - Attempting a reconnect in 0.67s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-14 07:01:00,480 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 9 messages from #paxg (keep_pinned: False)
2025-07-14 07:19:38,124 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-07-14 07:22:32,927 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 4 messages from #paxg (keep_pinned: False)
2025-07-14 07:25:39,122 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-07-14 11:40:50,176 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1393566046698475681 responded with 429. Retrying in 0.61 seconds.
2025-07-14 11:40:51,396 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1393203584476250155 responded with 429. Retrying in 0.38 seconds.
2025-07-14 11:40:52,417 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1393159337492353105 responded with 429. Retrying in 0.37 seconds.
2025-07-14 11:40:53,457 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1392574495705006182 responded with 429. Retrying in 0.32 seconds.
2025-07-14 11:40:54,532 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1392122489538351195 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:40:55,494 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1392121271528984707 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:40:56,484 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1392121115299807345 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:40:57,400 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1392002875521110168 responded with 429. Retrying in 0.39 seconds.
2025-07-14 11:40:58,454 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391875630412599406 responded with 429. Retrying in 0.34 seconds.
2025-07-14 11:40:59,406 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391866109799694648 responded with 429. Retrying in 0.38 seconds.
2025-07-14 11:41:00,446 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391760020399718440 responded with 429. Retrying in 0.35 seconds.
2025-07-14 11:41:01,439 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391759919056949248 responded with 429. Retrying in 0.35 seconds.
2025-07-14 11:41:02,421 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391759880721010719 responded with 429. Retrying in 0.37 seconds.
2025-07-14 11:41:03,414 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391744984738300004 responded with 429. Retrying in 0.38 seconds.
2025-07-14 11:41:04,460 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391715902470492210 responded with 429. Retrying in 0.34 seconds.
2025-07-14 11:41:05,479 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391658406397345887 responded with 429. Retrying in 0.32 seconds.
2025-07-14 11:41:06,410 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391646822858100776 responded with 429. Retrying in 0.38 seconds.
2025-07-14 11:41:07,512 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391620971273654273 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:08,482 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391405445238886528 responded with 429. Retrying in 0.31 seconds.
2025-07-14 11:41:09,504 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391397528645271604 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:10,439 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391395920154857472 responded with 429. Retrying in 0.35 seconds.
2025-07-14 11:41:11,438 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391382829560107018 responded with 429. Retrying in 0.36 seconds.
2025-07-14 11:41:12,411 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391361259856859201 responded with 429. Retrying in 0.38 seconds.
2025-07-14 11:41:13,480 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391296377165713411 responded with 429. Retrying in 0.32 seconds.
2025-07-14 11:41:16,384 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391040686698135645 responded with 429. Retrying in 0.41 seconds.
2025-07-14 11:41:17,521 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391040580800483469 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:18,473 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1391017400916250634 responded with 429. Retrying in 0.32 seconds.
2025-07-14 11:41:19,460 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1390690034021171361 responded with 429. Retrying in 0.33 seconds.
2025-07-14 11:41:20,537 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1390689631007277096 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:21,563 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1390689594101465118 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:22,526 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1390676759317581884 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:23,458 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1390676652731924666 responded with 429. Retrying in 0.34 seconds.
2025-07-14 11:41:24,443 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1390016911282016405 responded with 429. Retrying in 0.35 seconds.
2025-07-14 11:41:25,433 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1389844276099420271 responded with 429. Retrying in 0.36 seconds.
2025-07-14 11:41:26,465 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1389558232309301291 responded with 429. Retrying in 0.33 seconds.
2025-07-14 11:41:27,453 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1389298603750850681 responded with 429. Retrying in 0.34 seconds.
2025-07-14 11:41:28,528 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1389298447265566740 responded with 429. Retrying in 0.31 seconds.
2025-07-14 11:41:29,573 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1389260276955615263 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:30,509 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1389260213974208586 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:31,545 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1389232698521817118 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:32,519 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1388857684643676190 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:33,481 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1388856171246391397 responded with 429. Retrying in 0.31 seconds.
2025-07-14 11:41:37,485 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1388453299379896383 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:38,474 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1388453244660875354 responded with 429. Retrying in 0.33 seconds.
2025-07-14 11:41:39,439 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1388188340846596248 responded with 429. Retrying in 0.35 seconds.
2025-07-14 11:41:40,493 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1388091415262658661 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:41,450 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1388091356169244673 responded with 429. Retrying in 0.35 seconds.
2025-07-14 11:41:42,440 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1387777223825817711 responded with 429. Retrying in 0.36 seconds.
2025-07-14 11:41:43,501 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1386700925204435044 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:44,518 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1386315033515921480 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:45,432 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1386263054575996989 responded with 429. Retrying in 0.37 seconds.
2025-07-14 11:41:46,437 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1385969701670096987 responded with 429. Retrying in 0.35 seconds.
2025-07-14 11:41:49,459 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1385952705142456393 responded with 429. Retrying in 0.35 seconds.
2025-07-14 11:41:51,560 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1385256221094838272 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:52,525 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1384888465484681326 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:54,331 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1384849310184898560 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:55,674 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1383791225726832863 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:56,589 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1383425204159512679 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:57,563 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1381613482935455745 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:58,624 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1381611291726053389 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:41:59,549 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1381606542490472521 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:42:00,550 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1381212618815045693 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:42:01,572 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1381207574820552839 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:42:02,554 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1381207198323052554 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:42:03,484 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1381207073395707966 responded with 429. Retrying in 0.31 seconds.
2025-07-14 11:42:04,723 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1381200794556301323 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:42:05,711 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1380882899494113382 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:42:06,662 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1380559072033636382 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:42:07,678 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1380163744713412779 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:42:08,823 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1379798763547136060 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:42:09,777 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1379450229367111881 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:42:10,717 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376102562951860294/messages/1378901204998033529 responded with 429. Retrying in 0.30 seconds.
2025-07-14 11:42:11,982 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 92 messages from #💬-general (keep_pinned: True)
2025-07-14 11:43:38,130 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-07-14 13:23:36,174 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 7 messages from #paxg (keep_pinned: True)
2025-07-14 13:34:44,965 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 43.7s behind.
2025-07-14 17:36:24,702 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=94228be1f43d1d652c2223f3974229cdbb25aae90529636276f53e55f50085cf
2025-07-14 17:36:24,703 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=94228be1f43d1d652c2223f3974229cdbb25aae90529636276f53e55f50085cf (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=94228be1f43d1d652c2223f3974229cdbb25aae90529636276f53e55f50085cf)
2025-07-14 18:45:17,035 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=95649dfbf56a69f572d885c5fef74e96187908f99ae805b211d41958cf43a0ba
2025-07-14 21:08:25,841 - discord.client - ERROR - Attempting a reconnect in 1.75s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-14 21:19:27,013 - discord.client - ERROR - Attempting a reconnect in 2.61s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-14 22:14:52,103 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bc542cee974c6d0062dddf21af69c78d2779ee4baeed40d97a34402204a6fde0
2025-07-14 22:14:52,104 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bc542cee974c6d0062dddf21af69c78d2779ee4baeed40d97a34402204a6fde0 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bc542cee974c6d0062dddf21af69c78d2779ee4baeed40d97a34402204a6fde0)
2025-07-14 22:14:59,161 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.3s behind.
2025-07-14 22:24:19,431 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7da46cd54f1d270dc13f24634645ad2ba3ac5300de1c78a579ed3e3e87077119
2025-07-15 04:00:57,336 - services.market.short_term_price_alert_service - ERROR - Error fetching OHLCV data for BTCUSDT 1h: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1h&limit=2&symbol=BTCUSDT
2025-07-15 07:41:19,383 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=9507717486f376853d4784e80a56a3d5d3a823d6c1f9dd28ef7fffbd2410ad00
2025-07-15 11:27:39,359 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=700e5314bffc0f5f885344093e77a015f49f64b5975234278c62a103bc5eb09c
2025-07-15 11:40:04,099 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=db0ff462b6de91b21862d202f3623e1d21250ab2b5b69de633c91a772b327558
2025-07-15 11:40:04,100 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=db0ff462b6de91b21862d202f3623e1d21250ab2b5b69de633c91a772b327558 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=db0ff462b6de91b21862d202f3623e1d21250ab2b5b69de633c91a772b327558)
2025-07-15 12:42:09,488 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #mexc-auto (keep_pinned: True)
2025-07-15 12:59:21,981 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=152af7958c6d217b2d7c9aae4f773e55dff58a1918407cc32c1acdbb1f8f4205
2025-07-15 13:15:18,835 - discord.client - ERROR - Attempting a reconnect in 1.42s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-15 13:19:09,360 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e814515ba4a4d86902ff2e062e743ae817d6b4df8ce6e21951c0938df9e92ab8
2025-07-15 14:44:55,823 - discord.client - ERROR - Attempting a reconnect in 1.18s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-15 16:19:51,498 - discord.client - ERROR - Attempting a reconnect in 1.32s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-15 17:01:22,848 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 558, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 309, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 747, in send
    r.content
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 899, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 816, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 572, in stream
    for line in self.read_chunked(amt, decode_content=decode_content):
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 764, in read_chunked
    self._update_chunk_length()
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 694, in _update_chunk_length
    line = self._fp.fp.readline()
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-07-15 17:01:32,852 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 558, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 309, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 747, in send
    r.content
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 899, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
  File "/usr/local/lib/python3.10/dist-packages/requests/models.py", line 816, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 572, in stream
    for line in self.read_chunked(amt, decode_content=decode_content):
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 764, in read_chunked
    self._update_chunk_length()
  File "/usr/lib/python3/dist-packages/urllib3/response.py", line 694, in _update_chunk_length
    line = self._fp.fp.readline()
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-07-15 17:01:36,467 - services.trading.trading_service - ERROR - Get orders error: binance GET https://fapi.binance.com/fapi/v1/openOrders?timestamp=1752598866315&recvWindow=10000&signature=609b48e68be25baebac307a57cf7f93bda4106413c669ecead0b46d5a73cf06e
2025-07-15 17:01:36,471 - discord.client - ERROR - Attempting a reconnect in 0.07s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-15 17:22:50,018 - discord.client - ERROR - Attempting a reconnect in 0.27s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-15 18:47:27,522 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 44.2s behind.
2025-07-15 19:06:53,576 - discord.client - ERROR - Attempting a reconnect in 1.53s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 659, in connect
    await self.ws.poll_event()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 646, in poll_event
    raise ConnectionClosed(self.socket, shard_id=self.shard_id, code=code) from None
discord.errors.ConnectionClosed: Shard ID None WebSocket closed with 1000
2025-07-15 19:29:59,409 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0ad606a2543c3e691396f881139d7ce894e14fb7e058988a27f5f5c31c442c5a
2025-07-16 00:24:55,230 - discord.gateway - WARNING - Can't keep up, shard ID None websocket is 43.6s behind.
2025-07-16 07:23:03,124 - services.market.market_monitor_service - ERROR - Error fetching flexible products: 
2025-07-16 12:16:43,324 - __main__ - WARNING - Slow command execution: watchlist took 7.78s
2025-07-16 12:31:57,271 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x7f3238c89960>, 256274.*********)]']
connector: <aiohttp.connector.TCPConnector object at 0x7f325ee62860>
2025-07-16 12:37:17,743 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 5 messages from #trade-h (keep_pinned: False)
2025-07-16 12:37:25,755 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 4 messages from #trade (keep_pinned: False)
2025-07-16 12:38:06,177 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 1/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:38:06,186 - handlers.discord.trading.trade_h_commands - WARNING - Trade-H status message was deleted, recreating...
2025-07-16 12:38:08,502 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 2/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:38:12,856 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 3/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:38:12,857 - handlers.discord.trading.advanced_commands - ERROR - ❌ Failed to update status message after 3 attempts: 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:39:06,232 - handlers.discord.trading.trade_h_commands - WARNING - Trade-H status message was deleted, recreating...
2025-07-16 12:39:06,236 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 1/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:39:08,548 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 2/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:39:12,955 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 3/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:39:12,957 - handlers.discord.trading.advanced_commands - ERROR - ❌ Failed to update status message after 3 attempts: 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:40:06,181 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 1/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:40:08,554 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 2/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:40:12,872 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 3/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:40:12,878 - handlers.discord.trading.advanced_commands - ERROR - ❌ Failed to update status message after 3 attempts: 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:41:06,236 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 1/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:41:08,671 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 2/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:41:12,993 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 3/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:41:12,994 - handlers.discord.trading.advanced_commands - ERROR - ❌ Failed to update status message after 3 attempts: 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:42:06,161 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 1/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:42:08,533 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 2/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:42:12,876 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 3/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-16 12:42:12,878 - handlers.discord.trading.advanced_commands - ERROR - ❌ Failed to update status message after 3 attempts: 404 Not Found (error code: 10008): Unknown Message
2025-07-16 13:04:05,297 - __main__ - ERROR - Error loading extensions: Extension 'handlers.discord.market.price_alert_commands' raised an error: SyntaxError: invalid syntax (price_alert_commands.py, line 10)
2025-07-16 13:04:46,066 - __main__ - WARNING - Slow command execution: watchlist took 7.72s
2025-07-16 13:05:05,714 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x7fd8df2f6140>, 258262.*********)]']
connector: <aiohttp.connector.TCPConnector object at 0x7fd8fc2b2ce0>
