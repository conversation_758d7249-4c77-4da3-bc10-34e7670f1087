#!/usr/bin/env python3
"""
Script to analyze unused functions and imports in the codebase
"""

import ast
import os
import re
from collections import defaultdict
from typing import Dict, Set, List, Tuple

class CodeAnalyzer:
    def __init__(self, root_dir: str = "."):
        self.root_dir = root_dir
        self.python_files = []
        self.function_definitions = defaultdict(set)  # file -> set of function names
        self.function_calls = defaultdict(set)  # file -> set of function calls
        self.imports = defaultdict(set)  # file -> set of imported names
        self.import_usage = defaultdict(set)  # file -> set of used imported names
        self.class_methods = defaultdict(dict)  # file -> class -> set of methods
        
    def find_python_files(self):
        """Find all Python files in the project"""
        for root, dirs, files in os.walk(self.root_dir):
            # Skip __pycache__ directories
            dirs[:] = [d for d in dirs if d != '__pycache__']
            
            for file in files:
                if file.endswith('.py'):
                    filepath = os.path.join(root, file)
                    self.python_files.append(filepath)
    
    def analyze_file(self, filepath: str):
        """Analyze a single Python file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            visitor = FunctionVisitor(filepath)
            visitor.visit(tree)
            
            self.function_definitions[filepath] = visitor.function_definitions
            self.function_calls[filepath] = visitor.function_calls
            self.imports[filepath] = visitor.imports
            self.import_usage[filepath] = visitor.import_usage
            self.class_methods[filepath] = visitor.class_methods
            
        except Exception as e:
            print(f"Error analyzing {filepath}: {e}")
    
    def analyze_all_files(self):
        """Analyze all Python files"""
        self.find_python_files()
        print(f"Found {len(self.python_files)} Python files")
        
        for filepath in self.python_files:
            self.analyze_file(filepath)
    
    def find_unused_functions(self) -> Dict[str, Set[str]]:
        """Find functions that are defined but never called"""
        unused_functions = defaultdict(set)
        
        # Collect all function calls across all files
        all_calls = set()
        for calls in self.function_calls.values():
            all_calls.update(calls)
        
        # Check each function definition
        for filepath, functions in self.function_definitions.items():
            for func_name in functions:
                # Skip special methods and common patterns
                if (func_name.startswith('__') or 
                    func_name in ['main', 'setup', 'teardown'] or
                    func_name.startswith('test_') or
                    func_name.startswith('_')):  # Private methods might be used internally
                    continue
                
                if func_name not in all_calls:
                    unused_functions[filepath].add(func_name)
        
        return unused_functions
    
    def find_unused_imports(self) -> Dict[str, Set[str]]:
        """Find imports that are never used"""
        unused_imports = defaultdict(set)
        
        for filepath, imports in self.imports.items():
            used = self.import_usage[filepath]
            for import_name in imports:
                if import_name not in used:
                    unused_imports[filepath].add(import_name)
        
        return unused_imports
    
    def generate_report(self):
        """Generate analysis report"""
        print("\n" + "="*60)
        print("CODE ANALYSIS REPORT")
        print("="*60)
        
        unused_functions = self.find_unused_functions()
        unused_imports = self.find_unused_imports()
        
        print(f"\n📊 SUMMARY:")
        print(f"  - Total Python files: {len(self.python_files)}")
        print(f"  - Files with unused functions: {len(unused_functions)}")
        print(f"  - Files with unused imports: {len(unused_imports)}")
        
        if unused_functions:
            print(f"\n🔍 UNUSED FUNCTIONS:")
            for filepath, functions in unused_functions.items():
                if functions:
                    print(f"\n  📄 {filepath}:")
                    for func in sorted(functions):
                        print(f"    - {func}()")
        
        if unused_imports:
            print(f"\n📦 UNUSED IMPORTS:")
            for filepath, imports in unused_imports.items():
                if imports:
                    print(f"\n  📄 {filepath}:")
                    for imp in sorted(imports):
                        print(f"    - {imp}")

class FunctionVisitor(ast.NodeVisitor):
    def __init__(self, filepath: str):
        self.filepath = filepath
        self.function_definitions = set()
        self.function_calls = set()
        self.imports = set()
        self.import_usage = set()
        self.class_methods = defaultdict(set)
        self.current_class = None
    
    def visit_FunctionDef(self, node):
        """Visit function definitions"""
        if self.current_class:
            self.class_methods[self.current_class].add(node.name)
        else:
            self.function_definitions.add(node.name)
        self.generic_visit(node)
    
    def visit_AsyncFunctionDef(self, node):
        """Visit async function definitions"""
        if self.current_class:
            self.class_methods[self.current_class].add(node.name)
        else:
            self.function_definitions.add(node.name)
        self.generic_visit(node)
    
    def visit_ClassDef(self, node):
        """Visit class definitions"""
        old_class = self.current_class
        self.current_class = node.name
        self.generic_visit(node)
        self.current_class = old_class
    
    def visit_Call(self, node):
        """Visit function calls"""
        if isinstance(node.func, ast.Name):
            self.function_calls.add(node.func.id)
            self.import_usage.add(node.func.id)
        elif isinstance(node.func, ast.Attribute):
            if isinstance(node.func.value, ast.Name):
                self.import_usage.add(node.func.value.id)
        self.generic_visit(node)
    
    def visit_Import(self, node):
        """Visit import statements"""
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name
            self.imports.add(name)
    
    def visit_ImportFrom(self, node):
        """Visit from...import statements"""
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name
            self.imports.add(name)
    
    def visit_Name(self, node):
        """Visit name references"""
        if isinstance(node.ctx, ast.Load):
            self.import_usage.add(node.id)
        self.generic_visit(node)
    
    def visit_Attribute(self, node):
        """Visit attribute access"""
        if isinstance(node.value, ast.Name):
            self.import_usage.add(node.value.id)
        self.generic_visit(node)

if __name__ == "__main__":
    analyzer = CodeAnalyzer()
    analyzer.analyze_all_files()
    analyzer.generate_report()
