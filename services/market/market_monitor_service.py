import asyncio
import logging
import hmac
import hashlib
import threading
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from utils.config import get_binance_credentials
from services.core.http_client_service import get_http_client

logger = logging.getLogger(__name__)

class MarketMonitorService:
    """Service for monitoring market conditions."""

    def __init__(self):
        self.is_running = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.logger = logger

        # Cache for rates data
        self._rates_cache: Dict[str, Any] = {}
        self._cache_timestamp: Optional[datetime] = None
        self._cache_duration = timedelta(minutes=5)  # Cache for 5 minutes

        # Load monitoring thresholds from config
        from utils.config import load_config
        config = load_config()
        market_monitor_config = config.get('market_monitor', {})
        self.earn_thresholds = market_monitor_config.get('earn_fixed_thresholds', [10, 15, 20, 25])
        self.p2p_thresholds = market_monitor_config.get('p2p_increase_thresholds', [2, 5, 10])

        # Previous rates for comparison
        self._previous_earn_rate = 0.0
        self._previous_p2p_buy_rate = 0.0

        # Alert callbacks
        self.alert_callbacks = []

    async def start_monitoring(self):
        """Start market monitoring."""
        if self.is_running:
            self.logger.warning("Market monitor is already running")
            return

        self.is_running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())

    async def stop_monitoring(self):
        """Stop market monitoring."""
        if not self.is_running:
            return

        self.is_running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass

    async def _monitor_loop(self):
        """Main monitoring loop."""
        while self.is_running:
            try:
                # Get current rates
                rates_data = await self.get_current_rates()

                if rates_data and not rates_data.get('error'):
                    # Check earn rate alerts
                    await self._check_earn_rate_alerts(rates_data)

                    # Check P2P rate alerts
                    await self._check_p2p_rate_alerts(rates_data)
                else:
                    self.logger.warning(f"No valid rates data received: {rates_data}")

                await asyncio.sleep(300)  # Check every 5 minutes

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in market monitor loop: {e}")
                import traceback
                self.logger.error(f"Traceback: {traceback.format_exc()}")
                await asyncio.sleep(300)

    async def _fetch_p2p_rates(self) -> Dict[str, Any]:
        """Fetch P2P USDT/VND rates from Binance P2P API."""
        try:
            url = "https://p2p.binance.com/bapi/c2c/v2/friendly/c2c/adv/search"

            # Headers to mimic browser request
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            # Request data for BUY orders (VND -> USDT)
            buy_data = {
                "fiat": "VND",
                "page": 1,
                "rows": 5,
                "tradeType": "BUY",
                "asset": "USDT",
                "countries": [],
                "proMerchant": None,
                "shieldMerchantAds": False,
                "filterType": "all",
                "additionalKycVerifyFilter": 0,
                "publisherType": None,
                "payTypes": [],
                "classifies": ["mass", "profession"]
            }

            # Request data for SELL orders (USDT -> VND)
            sell_data = {
                "fiat": "VND",
                "page": 1,
                "rows": 5,
                "tradeType": "SELL",
                "asset": "USDT",
                "countries": [],
                "proMerchant": None,
                "shieldMerchantAds": False,
                "filterType": "all",
                "additionalKycVerifyFilter": 0,
                "publisherType": None,
                "payTypes": [],
                "classifies": ["mass", "profession"]
            }

            http_client = await get_http_client()

            # Fetch buy rates
            async with http_client.request('POST', url, headers=headers, json=buy_data, timeout=10) as buy_response:
                if buy_response.status == 200:
                    buy_result = await buy_response.json()
                    buy_ads = buy_result.get('data', [])
                    buy_price = float(buy_ads[0]['adv']['price']) if buy_ads else 0
                else:
                    buy_price = 0

            # Fetch sell rates
            async with http_client.request('POST', url, headers=headers, json=sell_data, timeout=10) as sell_response:
                if sell_response.status == 200:
                    sell_result = await sell_response.json()
                    sell_ads = sell_result.get('data', [])
                    sell_price = float(sell_ads[0]['adv']['price']) if sell_ads else 0
                else:
                    sell_price = 0

            if buy_price > 0 and sell_price > 0:
                # Use unified percentage calculation service for spread
                from services.market.percentage_calculation_service import get_percentage_service
                percentage_service = get_percentage_service()

                result = percentage_service.calculate_percentage_change(buy_price, sell_price)
                spread = result.value if result.is_valid else 0.0

                return {
                    'buy_price': buy_price,
                    'sell_price': sell_price,
                    'spread': round(spread, 2),
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {}

        except Exception as e:
            self.logger.error(f"Error fetching P2P rates: {e}")
            return {}

    def _create_binance_signature(self, query_string: str, api_secret: str) -> str:
        """Create HMAC SHA256 signature for Binance API."""
        return hmac.new(
            api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    async def _fetch_earn_rates(self) -> Dict[str, Any]:
        """Fetch Binance Earn rates for USDT using real API."""
        try:
            credentials = get_binance_credentials()
            api_key = credentials.get('api_key', '')
            api_secret = credentials.get('api_secret', '')

            if not api_key or not api_secret:
                self.logger.warning("Binance API credentials not found in config")
                return {
                    'flexible': {'rate': 0, 'note': 'API credentials not configured'}
                }

            base_url = "https://api.binance.com"

            # Get flexible savings products only
            flexible_data = await self._fetch_flexible_products(base_url, api_key, api_secret)

            return {
                'flexible': flexible_data,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error fetching Earn rates: {e}")
            return {
                'flexible': {'rate': 0, 'note': f'API Error: {str(e)}'}
            }

    async def _fetch_flexible_products(self, base_url: str, api_key: str, api_secret: str) -> Dict[str, Any]:
        """Fetch flexible savings products from Binance."""
        try:
            endpoint = "/sapi/v1/simple-earn/flexible/list"
            timestamp = int(time.time() * 1000)

            # Parameters
            params = {
                'asset': 'USDT',
                'timestamp': timestamp
            }

            # Create query string
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])

            # Create signature
            signature = self._create_binance_signature(query_string, api_secret)
            params['signature'] = signature

            # Headers
            headers = {
                'X-MBX-APIKEY': api_key,
                'Content-Type': 'application/json'
            }

            # Make request
            url = f"{base_url}{endpoint}"
            http_client = await get_http_client()
            async with http_client.request('GET', url, params=params, headers=headers, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    rows = data.get('rows', [])

                    if rows:
                        # Get USDT flexible product
                        usdt_product = next((p for p in rows if p.get('asset') == 'USDT'), None)
                        if usdt_product:
                            rate_str = usdt_product.get('latestAnnualPercentageRate', '0')
                            rate = float(rate_str) if rate_str else 0
                            return {
                                'rate': rate,
                                'product_id': usdt_product.get('productId', ''),
                                'status': usdt_product.get('status', ''),
                                'note': 'Real-time from Binance API'
                            }

                    return {'rate': 0, 'note': 'USDT flexible product not found'}
                else:
                    response_text = await response.text()
                    self.logger.error(f"Binance API error: {response.status} - {response_text}")
                    return {'rate': 0, 'note': f'API Error: {response.status}'}

        except Exception as e:
            self.logger.error(f"Error fetching flexible products: {e}")
            return {'rate': 0, 'note': f'Error: {str(e)}'}



    def _is_cache_valid(self) -> bool:
        """Check if cache is still valid."""
        if self._cache_timestamp is None:
            return False
        return datetime.now() - self._cache_timestamp < self._cache_duration

    async def get_current_rates(self):
        """Get current rates data from real Binance API with caching."""
        try:
            # Check cache first
            if self._is_cache_valid() and self._rates_cache:
                self.logger.info("Returning cached rates data")
                return self._rates_cache

            self.logger.info("Fetching fresh rates data from APIs")

            # Fetch data from APIs
            p2p_rates = await self._fetch_p2p_rates()
            earn_rates = await self._fetch_earn_rates()

            # Prepare result (match expected keys in market_commands.py)
            # Use consistent spread calculation from p2p_rates
            p2p_spread = p2p_rates.get('spread', 0) if p2p_rates else 0

            result = {
                'earn': earn_rates,
                'p2p': {
                    'buy_rate': p2p_rates.get('buy_price', 0),
                    'sell_rate': p2p_rates.get('sell_price', 0),
                    'spread': p2p_spread
                },
                'last_updated': datetime.now().isoformat(),
                'cache_duration_minutes': self._cache_duration.total_seconds() / 60
            }

            # Update cache
            self._rates_cache = result
            self._cache_timestamp = datetime.now()

            self.logger.info(f"Successfully fetched rates data. P2P: {bool(p2p_rates)}, Earn: {bool(earn_rates)}")
            return result

        except Exception as e:
            self.logger.error(f"Error fetching rates data: {e}")
            # Return cached data if available, otherwise empty
            if self._rates_cache:
                self.logger.info("Returning cached data due to error")
                return self._rates_cache
            return {
                'earn': {},
                'p2p': {},
                'error': str(e)
            }

    async def _check_earn_rate_alerts(self, rates_data: Dict[str, Any]):
        """Check for earn rate alerts"""
        try:
            earn_data = rates_data.get('earn', {})
            flexible_data = earn_data.get('flexible', {})
            current_rate = flexible_data.get('rate', 0)

            if current_rate > 0 and self._previous_earn_rate > 0:
                # Check if rate increased significantly
                rate_increase = current_rate - self._previous_earn_rate
                rate_increase_pct = (rate_increase / self._previous_earn_rate) * 100

                # Check against thresholds
                for threshold in sorted(self.earn_thresholds):
                    if current_rate >= threshold and self._previous_earn_rate < threshold:
                        await self._trigger_earn_rate_alert(
                            current_rate, self._previous_earn_rate, threshold, "threshold_reached"
                        )
                        break

                # Check for significant increases
                for threshold in [5, 10, 20]:  # % increase thresholds
                    if rate_increase_pct >= threshold:
                        await self._trigger_earn_rate_alert(
                            current_rate, self._previous_earn_rate, threshold, "rate_increase"
                        )
                        break

            self._previous_earn_rate = current_rate

        except Exception as e:
            self.logger.error(f"Error checking earn rate alerts: {e}")

    async def _check_p2p_rate_alerts(self, rates_data: Dict[str, Any]):
        """Check for P2P rate alerts - only monitors buy rate (buyer demand)"""
        try:
            p2p_data = rates_data.get('p2p', {})
            current_buy = p2p_data.get('buy_rate', 0)

            if current_buy > 0 and self._previous_p2p_buy_rate > 0:
                # Check buy rate increase (buyer demand indicator) using unified service
                from services.market.percentage_calculation_service import get_percentage_service
                percentage_service = get_percentage_service()

                result = percentage_service.calculate_percentage_change(current_buy, self._previous_p2p_buy_rate)
                buy_increase_pct = result.value if result.is_valid else 0.0

                # Check against thresholds [2, 5, 10]% - find the highest threshold exceeded
                triggered_threshold = None
                for threshold in sorted(self.p2p_thresholds):
                    if buy_increase_pct >= threshold:
                        triggered_threshold = threshold

                if triggered_threshold is not None:
                    await self._trigger_p2p_rate_alert(
                        current_buy, self._previous_p2p_buy_rate, triggered_threshold, "buy_rate_increase"
                    )

            # Update previous buy rate for next comparison
            self._previous_p2p_buy_rate = current_buy

        except Exception as e:
            self.logger.error(f"Error checking P2P rate alerts: {e}")

    async def _trigger_earn_rate_alert(self, current_rate: float, previous_rate: float,
                                     threshold: float, alert_type: str):
        """Trigger earn rate alert"""
        try:
            message = f"🚨 EARN RATE ALERT: USDT Flexible rate "

            if alert_type == "threshold_reached":
                message += f"reached {threshold}% APY (was {previous_rate:.2f}%, now {current_rate:.2f}%)"
            elif alert_type == "rate_increase":
                # Use unified percentage calculation service
                from services.market.percentage_calculation_service import get_percentage_service
                percentage_service = get_percentage_service()

                result = percentage_service.calculate_percentage_change(current_rate, previous_rate)
                increase = result.value if result.is_valid else 0.0
                message += f"increased by {increase:.1f}% (from {previous_rate:.2f}% to {current_rate:.2f}%)"

            self.logger.info(message)

            # Trigger callbacks
            for callback in self.alert_callbacks:
                if callback:
                    try:
                        await callback("earn_rate", alert_type, current_rate, previous_rate, threshold)
                    except Exception as e:
                        self.logger.error(f"Error in earn rate alert callback: {e}")

        except Exception as e:
            self.logger.error(f"Error triggering earn rate alert: {e}")

    async def _trigger_p2p_rate_alert(self, current_rate: float, previous_rate: float,
                                    threshold: float, alert_type: str):
        """Trigger P2P rate alert - only for buy rate increases (buyer demand)"""
        try:
            # Use unified percentage calculation service
            from services.market.percentage_calculation_service import get_percentage_service
            percentage_service = get_percentage_service()

            result = percentage_service.calculate_percentage_change(current_rate, previous_rate)
            increase = result.value if result.is_valid else 0.0

            message = f"🚨 P2P RATE ALERT: USDT/VND buy rate increased by {increase:.1f}% "
            message += f"(from {previous_rate:,.0f} to {current_rate:,.0f} VND) - High buyer demand detected!"

            self.logger.info(message)

            # Trigger callbacks
            for callback in self.alert_callbacks:
                if callback:
                    try:
                        await callback("p2p_rate", alert_type, current_rate, previous_rate, threshold)
                    except Exception as e:
                        self.logger.error(f"Error in P2P rate alert callback: {e}")

        except Exception as e:
            self.logger.error(f"Error triggering P2P rate alert: {e}")

    def register_alert_callback(self, callback):
        """Register callback for rate alerts"""
        self.alert_callbacks.append(callback)
        return len(self.alert_callbacks) - 1

# Global instance
_market_monitor_service = None
_instance_lock = threading.RLock()

def get_market_monitor_service() -> MarketMonitorService:
    """Get the global market monitor service instance."""
    global _market_monitor_service
    with _instance_lock:
        if _market_monitor_service is None:
            _market_monitor_service = MarketMonitorService()
        return _market_monitor_service


