import sqlite3
import logging
import threading
from typing import Optional, List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseService:
    def __init__(self, db_path: str = "chartfix.db"):
        self.db_path = db_path
        self.lock = threading.RLock()
        self._init_database()

    def _init_database(self):
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()





                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS alert_cooldowns (
                        symbol TEXT PRIMARY KEY,
                        last_alert_time INTEGER
                    )
                ''')

                conn.commit()
                logger.info("Database initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise

    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.cursor()
                    cursor.execute(query, params)
                    return [dict(row) for row in cursor.fetchall()]
            except Exception as e:
                logger.error(f"Error executing query: {e}")
                raise

    def execute_update(self, query: str, params: tuple = ()) -> int:
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute(query, params)
                    conn.commit()
                    return cursor.rowcount
            except Exception as e:
                logger.error(f"Error executing update: {e}")
                raise


