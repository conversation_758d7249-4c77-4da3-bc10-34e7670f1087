import logging
import ccxt
import asyncio
from typing import Dict, List
from datetime import datetime, timezone

from utils.config import get_trade_h_credentials

def safe_float(value, default=0):
    """Safely convert value to float"""
    if value is None or value == '':
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

logger = logging.getLogger(__name__)

class TradeHDataService:
    """Trade-H data service similar to TradingDataService"""

    def __init__(self):
        self.exchange = None
        self.last_update = None
        self.cached_data = {
            'account': {},
            'positions': [],
            'orders': []
        }
        self.update_interval = 60  # seconds
        self.running = False
        self.update_task = None
        self._initialize_exchange()

    def _initialize_exchange(self):
        """Initialize Binance Futures exchange with Trade-H credentials"""
        try:
            credentials = get_trade_h_credentials()

            api_key = credentials.get('api_key', '')
            api_secret = credentials.get('api_secret', '')
            use_testnet = credentials.get('testnet', False)

            if not api_key or not api_secret:
                raise ValueError("Trade-H Binance API credentials not found in config")

            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': api_secret,
                'timeout': 30000,
                'enableRateLimit': True,
                'sandbox': use_testnet,
                'options': {
                    'defaultType': 'future',
                    'warnOnFetchOpenOrdersWithoutSymbol': False
                }
            })

            self.exchange.load_markets()
            logger.info(f"Trade-H Binance Futures initialized (testnet: {use_testnet})")

        except Exception as e:
            logger.error(f"Failed to initialize Trade-H Binance Futures: {e}")
            raise

    async def start_data_updates(self):
        """Start periodic data updates"""
        if self.running:
            return

        self.running = True
        self.update_task = asyncio.create_task(self._update_loop())
        logger.info("🔄 Trade-H data service started - updating every 60 seconds")

    async def stop_data_updates(self):
        """Stop periodic data updates"""
        self.running = False
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
        logger.info("⏹️ Trade-H data service stopped")

    async def _update_loop(self):
        """Main update loop"""
        while self.running:
            try:
                await self.update_all_data()
                await asyncio.sleep(self.update_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in Trade-H data update loop: {e}")
                await asyncio.sleep(5)  # Short delay before retry

    async def update_all_data(self):
        """Update all trading data similar to TradingDataService"""
        try:
            if not self.exchange:
                logger.warning("⚠️ Trade-H exchange not available")
                return

            # Get account balance
            try:
                balance = self.exchange.fetch_balance()
                self.cached_data['account'] = {
                    'total_usdt': safe_float(balance.get('USDT', {}).get('total', 0)),
                    'free_usdt': safe_float(balance.get('USDT', {}).get('free', 0)),
                    'used_usdt': safe_float(balance.get('USDT', {}).get('used', 0))
                }
            except Exception as e:
                logger.error(f"❌ Error fetching Trade-H account balance: {e}")
                self.cached_data['account'] = {}

            # Get positions
            try:
                positions = self.exchange.fetch_positions()
                active_positions = []

                for pos in positions:
                    contracts = safe_float(pos.get('contracts', 0))
                    if abs(contracts) > 0:
                        # Get position side from API
                        api_side = pos.get('side', '').lower()

                        # Calculate position side based on contracts
                        calculated_side = 'LONG' if contracts > 0 else 'SHORT'

                        # Use API side if available and valid, otherwise use calculated
                        if api_side in ['long', 'short']:
                            position_side = api_side.upper()
                        else:
                            position_side = calculated_side

                        symbol = pos.get('symbol', 'Unknown')
                        logger.debug(f"Trade-H Position {symbol}: contracts={contracts}, api_side={api_side}, final_position_side={position_side}")

                        active_positions.append({
                            'symbol': symbol,
                            'side': api_side,
                            'position_side': position_side,
                            'size': abs(contracts),
                            'entry_price': safe_float(pos.get('entryPrice')),
                            'mark_price': safe_float(pos.get('markPrice')),
                            'unrealized_pnl': safe_float(pos.get('unrealizedPnl')),
                            'percentage': safe_float(pos.get('percentage'))
                        })

                self.cached_data['positions'] = active_positions
            except Exception as e:
                logger.error(f"❌ Error fetching Trade-H positions: {e}")
                self.cached_data['positions'] = []

            # Get orders
            try:
                orders = self.exchange.fetch_open_orders()
                formatted_orders = []

                for order in orders:
                    # Get position side from order info
                    position_side = order.get('info', {}).get('positionSide', 'BOTH')
                    if position_side == 'BOTH':
                        # Determine position side from order side
                        order_side = order.get('side', '').lower()
                        position_side = 'LONG' if order_side == 'buy' else 'SHORT'

                    formatted_orders.append({
                        'id': order.get('id'),
                        'symbol': order.get('symbol'),
                        'side': order.get('side'),
                        'type': order.get('type'),
                        'position_side': position_side,
                        'amount': safe_float(order.get('amount')),
                        'price': safe_float(order.get('price')),
                        'status': order.get('status'),
                        'stop_price': safe_float(order.get('stopPrice')),
                        'trigger_price': safe_float(order.get('triggerPrice')),
                        'take_profit_price': safe_float(order.get('takeProfitPrice')),
                        'stop_loss_price': safe_float(order.get('stopLossPrice'))
                    })
                self.cached_data['orders'] = formatted_orders
            except Exception as e:
                logger.error(f"❌ Error fetching Trade-H orders: {e}")
                self.cached_data['orders'] = []

            self.last_update = datetime.now(timezone.utc)
            logger.debug(f"📊 Trade-H data updated: {len(self.cached_data['positions'])} positions, {len(self.cached_data['orders'])} orders")

        except Exception as e:
            logger.error(f"❌ Error updating Trade-H data: {e}")

    def get_account_data(self) -> Dict:
        """Get cached account data"""
        return self.cached_data.get('account', {})

    def get_positions_data(self) -> List[Dict]:
        """Get cached positions data"""
        return self.cached_data.get('positions', [])

    def get_orders_data(self) -> List[Dict]:
        """Get cached orders data"""
        return self.cached_data.get('orders', [])

    def _correlate_tp_sl_with_positions(self, positions: List[Dict], orders: List[Dict]) -> List[Dict]:
        """Correlate TP/SL orders with positions"""
        enhanced_positions = []

        for pos in positions:
            symbol = pos.get('symbol', '')
            position_side = pos.get('position_side', '')

            # Find TP/SL orders for this position
            tp_price = 0
            sl_price = 0

            for order in orders:
                order_symbol = order.get('symbol', '')
                order_position_side = order.get('position_side', '')
                order_type = order.get('type', '').lower()

                # Match symbol and position side
                if order_symbol == symbol and order_position_side == position_side:
                    if 'take_profit' in order_type:
                        tp_price = order.get('stop_price', 0) or order.get('trigger_price', 0) or order.get('price', 0)
                    elif 'stop' in order_type and 'take_profit' not in order_type:
                        sl_price = order.get('stop_price', 0) or order.get('trigger_price', 0) or order.get('price', 0)

            # Add TP/SL info to position
            enhanced_pos = pos.copy()
            enhanced_pos['take_profit_price'] = tp_price
            enhanced_pos['stop_loss_price'] = sl_price
            enhanced_positions.append(enhanced_pos)

        return enhanced_positions

    def get_summary_data(self) -> Dict:
        """Get summary of all data for status dashboard"""
        account = self.get_account_data()
        positions = self.get_positions_data()
        orders = self.get_orders_data()

        # Correlate TP/SL orders with positions
        enhanced_positions = self._correlate_tp_sl_with_positions(positions, orders)

        # Calculate totals
        total_unrealized_pnl = sum(pos.get('unrealized_pnl', 0) for pos in enhanced_positions)

        return {
            'account': {
                'total_balance': account.get('total_usdt', 0),
                'free_balance': account.get('free_usdt', 0),
                'margin_used': account.get('used_usdt', 0)
            },
            'positions': {
                'count': len(enhanced_positions),
                'total_unrealized_pnl': total_unrealized_pnl,
                'list': enhanced_positions
            },
            'orders': {
                'count': len(orders),
                'list': orders
            },
            'last_update': self.last_update.isoformat() if self.last_update else None
        }

    def is_data_fresh(self, max_age_seconds: int = 120) -> bool:
        """Check if cached data is fresh"""
        if not self.last_update:
            return False

        age = (datetime.now(timezone.utc) - self.last_update).total_seconds()
        return age <= max_age_seconds

    async def force_update(self):
        """Force immediate data update"""
        await self.update_all_data()

# Global Trade-H data service instance
trade_h_data_service = TradeHDataService()
